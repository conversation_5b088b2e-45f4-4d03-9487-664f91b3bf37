# LMArena Proxy - 可执行版本

## 使用说明

直接启动服务器
1. 运行 `LMArena-Proxy-Server.exe`
2. 等待服务器启动完成
3. 在浏览器中访问 http://localhost:9080/monitor

## 重要提示

1. **安装浏览器脚本**: 请确保已在Tampermonkey中安装了 `lmarena_injector.user.js` 脚本
2. **防火墙设置**: 首次运行时Windows可能会询问防火墙权限，请选择允许
3. **端口占用**: 默认使用9080端口，如果被占用请修改配置文件

## 文件说明

- `LMArena-Proxy-Server.exe`: 服务器程序
- `lmarena_injector.user.js`: 浏览器脚本
- `logs/`: 日志和配置文件目录
- `ngrok-v3-stable-windows-amd64/`: ngrok工具（如果包含）

## 原作者信息

- 作者: zhongruichen
- 项目地址: https://github.com/zhongruichen/lmarena-proxy
- 许可证: MIT License

## 故障排除

如果遇到问题，请：
1. 检查防火墙设置
2. 确认端口9080未被占用
3. 查看logs目录下的日志文件
4. 访问原项目地址获取最新信息
