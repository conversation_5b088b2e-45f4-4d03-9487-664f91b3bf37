/**
 * LMArena 代理服务器 - 浏览器扩展 Cookie 集成示例
 * 
 * 这个示例展示了如何在浏览器扩展中集成 Cookie 管理功能
 * 可以自动发送和接收 Cookie，保持登录状态
 */

class LMArenaProxyClient {
    constructor(serverUrl = 'ws://localhost:9080/ws') {
        this.serverUrl = serverUrl;
        this.ws = null;
        this.connected = false;
        this.cookieDomain = 'lmarena.ai';
        
        this.connect();
        this.setupCookieMonitoring();
    }
    
    /**
     * 连接到代理服务器
     */
    connect() {
        try {
            this.ws = new WebSocket(this.serverUrl);
            
            this.ws.onopen = () => {
                console.log('✅ 已连接到 LMArena 代理服务器');
                this.connected = true;
                
                // 连接后立即发送当前 Cookie
                this.sendCurrentCookies();
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };
            
            this.ws.onclose = () => {
                console.log('❌ 与代理服务器的连接已断开');
                this.connected = false;
                
                // 5秒后重连
                setTimeout(() => this.connect(), 5000);
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket 错误:', error);
            };
            
        } catch (error) {
            console.error('连接失败:', error);
            setTimeout(() => this.connect(), 5000);
        }
    }
    
    /**
     * 处理来自服务器的消息
     */
    handleMessage(message) {
        switch (message.type) {
            case 'cookie_request':
                // 服务器请求 Cookie
                this.sendCurrentCookies();
                break;
                
            case 'cookie_response':
                // 服务器发送 Cookie
                this.applyCookies(message.cookies);
                break;
                
            case 'cookie_update_ack':
                console.log(`🍪 Cookie 更新确认: ${message.domain} (${message.count} 个)`);
                break;
                
            default:
                // 其他消息类型的处理
                break;
        }
    }
    
    /**
     * 发送当前页面的 Cookie 到服务器
     */
    async sendCurrentCookies() {
        if (!this.connected) return;
        
        try {
            // 获取当前域名的所有 Cookie
            const cookies = await this.getCurrentCookies();
            
            if (Object.keys(cookies).length > 0) {
                const message = {
                    type: 'cookie_update',
                    domain: this.cookieDomain,
                    cookies: cookies
                };
                
                this.ws.send(JSON.stringify(message));
                console.log(`🍪 已发送 ${Object.keys(cookies).length} 个 Cookie 到服务器`);
            }
            
        } catch (error) {
            console.error('发送 Cookie 失败:', error);
        }
    }
    
    /**
     * 获取当前页面的 Cookie
     */
    async getCurrentCookies() {
        const cookies = {};
        
        // 方法1: 使用 document.cookie (适用于内容脚本)
        if (typeof document !== 'undefined') {
            document.cookie.split(';').forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    cookies[name] = decodeURIComponent(value);
                }
            });
        }
        
        // 方法2: 使用 Chrome 扩展 API (适用于后台脚本)
        if (typeof chrome !== 'undefined' && chrome.cookies) {
            try {
                const cookieList = await new Promise((resolve) => {
                    chrome.cookies.getAll({domain: this.cookieDomain}, resolve);
                });
                
                cookieList.forEach(cookie => {
                    cookies[cookie.name] = cookie.value;
                });
            } catch (error) {
                console.warn('无法使用 Chrome Cookie API:', error);
            }
        }
        
        return cookies;
    }
    
    /**
     * 应用服务器发送的 Cookie
     */
    async applyCookies(cookiesData) {
        try {
            // 过滤掉内部字段
            const cookies = Object.fromEntries(
                Object.entries(cookiesData).filter(([key]) => !key.startsWith('_'))
            );
            
            // 方法1: 使用 document.cookie (适用于内容脚本)
            if (typeof document !== 'undefined') {
                Object.entries(cookies).forEach(([name, value]) => {
                    document.cookie = `${name}=${encodeURIComponent(value)}; path=/; domain=${this.cookieDomain}`;
                });
            }
            
            // 方法2: 使用 Chrome 扩展 API (适用于后台脚本)
            if (typeof chrome !== 'undefined' && chrome.cookies) {
                for (const [name, value] of Object.entries(cookies)) {
                    try {
                        await new Promise((resolve, reject) => {
                            chrome.cookies.set({
                                url: `https://${this.cookieDomain}`,
                                name: name,
                                value: value,
                                domain: this.cookieDomain,
                                path: '/'
                            }, (cookie) => {
                                if (chrome.runtime.lastError) {
                                    reject(chrome.runtime.lastError);
                                } else {
                                    resolve(cookie);
                                }
                            });
                        });
                    } catch (error) {
                        console.warn(`设置 Cookie ${name} 失败:`, error);
                    }
                }
            }
            
            console.log(`🍪 已应用 ${Object.keys(cookies).length} 个 Cookie`);
            
        } catch (error) {
            console.error('应用 Cookie 失败:', error);
        }
    }
    
    /**
     * 设置 Cookie 监控
     */
    setupCookieMonitoring() {
        // 监控 Cookie 变化 (如果支持)
        if (typeof chrome !== 'undefined' && chrome.cookies && chrome.cookies.onChanged) {
            chrome.cookies.onChanged.addListener((changeInfo) => {
                if (changeInfo.cookie.domain.includes(this.cookieDomain)) {
                    console.log('🍪 检测到 Cookie 变化:', changeInfo);
                    
                    // 延迟发送更新，避免频繁发送
                    clearTimeout(this.cookieUpdateTimer);
                    this.cookieUpdateTimer = setTimeout(() => {
                        this.sendCurrentCookies();
                    }, 1000);
                }
            });
        }
        
        // 定期同步 Cookie (每5分钟)
        setInterval(() => {
            if (this.connected) {
                this.sendCurrentCookies();
            }
        }, 5 * 60 * 1000);
    }
    
    /**
     * 请求服务器的 Cookie
     */
    requestCookies() {
        if (!this.connected) return;
        
        const message = {
            type: 'cookie_request',
            domain: this.cookieDomain
        };
        
        this.ws.send(JSON.stringify(message));
        console.log('🍪 已请求服务器 Cookie');
    }
    
    /**
     * 发送聊天请求 (示例)
     */
    sendChatRequest(requestData) {
        if (!this.connected) {
            console.error('未连接到服务器');
            return;
        }
        
        // 生成请求ID
        const requestId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        const message = {
            request_id: requestId,
            payload: requestData,
            files_to_upload: []
        };
        
        this.ws.send(JSON.stringify(message));
        console.log(`📤 已发送聊天请求: ${requestId}`);
        
        return requestId;
    }
}

// 使用示例
const proxyClient = new LMArenaProxyClient();

// 页面加载完成后请求 Cookie
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            proxyClient.requestCookies();
        }, 2000);
    });
}

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LMArenaProxyClient;
}

// 全局变量供浏览器使用
if (typeof window !== 'undefined') {
    window.LMArenaProxyClient = LMArenaProxyClient;
}

console.log('🚀 LMArena 代理客户端已加载');
