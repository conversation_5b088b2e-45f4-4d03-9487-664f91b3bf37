#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LMArena Proxy 打包脚本
使用PyInstaller将项目打包成exe文件

原作者: zhongruichen
项目地址: https://github.com/zhongruichen/lmarena-proxy
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("✗ PyInstaller 未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller 安装失败: {e}")
        return False

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# GUI启动器
gui_a = Analysis(
    ['start_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lmarena_injector.user.js', '.'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('LICENSE', '.'),
        ('docs', 'docs'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'tkinter.messagebox',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

gui_pyz = PYZ(gui_a.pure, gui_a.zipped_data, cipher=block_cipher)

gui_exe = EXE(
    gui_pyz,
    gui_a.scripts,
    [],
    exclude_binaries=True,
    name='LMArena-Proxy-GUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

# 服务器程序
server_a = Analysis(
    ['proxy_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lmarena_injector.user.js', '.'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('LICENSE', '.'),
        ('docs', 'docs'),
    ],
    hiddenimports=[
        'uvicorn',
        'fastapi',
        'websockets',
        'aiohttp',
        'prometheus_client',
        'pyngrok',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

server_pyz = PYZ(server_a.pure, server_a.zipped_data, cipher=block_cipher)

server_exe = EXE(
    server_pyz,
    server_a.scripts,
    [],
    exclude_binaries=True,
    name='LMArena-Proxy-Server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 显示控制台窗口以查看日志
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

# 收集所有文件
coll = COLLECT(
    gui_exe,
    gui_a.binaries,
    gui_a.zipfiles,
    gui_a.datas,
    server_exe,
    server_a.binaries,
    server_a.zipfiles,
    server_a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='LMArena-Proxy',
)
'''
    
    with open('lmarena_proxy.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建 lmarena_proxy.spec 文件")

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    
    try:
        # 使用spec文件构建，添加--noconfirm参数自动确认
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", "lmarena_proxy.spec"]
        subprocess.check_call(cmd)
        print("✓ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到dist目录"""
    dist_dir = Path("dist/LMArena-Proxy")
    if not dist_dir.exists():
        print("✗ dist目录不存在")
        return False
    
    # 复制ngrok文件夹（如果存在）
    ngrok_dir = Path("ngrok-v3-stable-windows-amd64")
    if ngrok_dir.exists():
        shutil.copytree(ngrok_dir, dist_dir / "ngrok-v3-stable-windows-amd64", dirs_exist_ok=True)
        print("✓ 已复制 ngrok 文件")
    
    # 创建logs目录
    logs_dir = dist_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # 复制配置文件（如果存在）
    config_file = Path("logs/config.json")
    if config_file.exists():
        shutil.copy2(config_file, logs_dir / "config.json")
        print("✓ 已复制配置文件")
    
    # 创建启动脚本
    start_script = dist_dir / "启动GUI.bat"
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('cd /d "%~dp0"\n')
        f.write('LMArena-Proxy-GUI.exe\n')
        f.write('pause\n')
    
    start_server_script = dist_dir / "启动服务器.bat"
    with open(start_server_script, 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('cd /d "%~dp0"\n')
        f.write('LMArena-Proxy-Server.exe\n')
        f.write('pause\n')
    
    print("✓ 已创建启动脚本")
    
    return True

def create_readme():
    """创建使用说明"""
    dist_dir = Path("dist/LMArena-Proxy")
    readme_content = """# LMArena Proxy - 可执行版本

## 使用说明

### 方式一：使用GUI界面（推荐）
1. 双击 `启动GUI.bat` 或直接运行 `LMArena-Proxy-GUI.exe`
2. 在GUI界面中点击"启动服务器"按钮
3. 等待服务器启动完成
4. 点击"打开监控面板"查看状态
5. 点击"打开LMArena"访问LMArena网站

### 方式二：直接启动服务器
1. 双击 `启动服务器.bat` 或直接运行 `LMArena-Proxy-Server.exe`
2. 等待服务器启动完成
3. 在浏览器中访问 http://localhost:9080/monitor

## 重要提示

1. **安装浏览器脚本**: 请确保已在Tampermonkey中安装了 `lmarena_injector.user.js` 脚本
2. **防火墙设置**: 首次运行时Windows可能会询问防火墙权限，请选择允许
3. **端口占用**: 默认使用9080端口，如果被占用请修改配置文件

## 文件说明

- `LMArena-Proxy-GUI.exe`: GUI启动器
- `LMArena-Proxy-Server.exe`: 服务器程序
- `lmarena_injector.user.js`: 浏览器脚本
- `logs/`: 日志和配置文件目录
- `ngrok-v3-stable-windows-amd64/`: ngrok工具（如果包含）

## 原作者信息

- **作者**: zhongruichen
- **项目地址**: https://github.com/zhongruichen/lmarena-proxy
- **许可证**: MIT License
- **项目描述**: 一个功能强大的 LMArena 反向代理服务器，提供 OpenAI 兼容的 API 接口

## 源码获取

如需获取最新源码、报告问题或参与开发，请访问：
- GitHub仓库: https://github.com/zhongruichen/lmarena-proxy
- 问题反馈: https://github.com/zhongruichen/lmarena-proxy/issues
- 贡献指南: https://github.com/zhongruichen/lmarena-proxy#贡献

## 故障排除

如果遇到问题，请：
1. 检查防火墙设置
2. 确认端口9080未被占用
3. 查看logs目录下的日志文件
4. 访问原项目地址获取最新信息
"""
    
    readme_file = dist_dir / "使用说明.txt"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 已创建使用说明")

def main():
    """主函数"""
    print("LMArena Proxy 打包工具")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，请手动安装")
            return False
    
    # 创建spec文件
    create_spec_file()
    
    # 构建exe
    if not build_exe():
        return False
    
    # 复制额外文件
    if not copy_additional_files():
        return False
    
    # 创建说明文件
    create_readme()
    
    print("\n" + "=" * 50)
    print("✓ 打包完成！")
    print("输出目录: dist/LMArena-Proxy/")
    print("请查看 '使用说明.txt' 了解如何使用")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n打包失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生错误: {e}")
        sys.exit(1)
