# LMArena 代理服务器 - Cookie 和浏览器功能增强

## 新增功能概述

本次更新为 LMArena 代理服务器增加了以下功能：

### 1. 🍪 Cookie 管理功能
- **自动保存和使用 Cookie**：保持登录状态，提高使用体验
- **多域名支持**：可以管理不同域名的 Cookie
- **API 接口**：提供完整的 Cookie 管理 API
- **监控面板集成**：在监控面板中可以查看和管理 Cookie

### 2. 🌐 自动打开浏览器功能
- **服务器启动时自动打开浏览器**：直接访问 https://lmarena.ai/
- **可配置延迟**：避免服务器未完全启动就打开浏览器
- **跨平台支持**：支持 Windows、macOS、Linux
- **手动控制**：可以通过 API 或监控面板手动打开浏览器

## 配置选项

### 浏览器设置
```json
{
  "browser": {
    "auto_open": true,                    // 是否自动打开浏览器
    "target_url": "https://lmarena.ai/",  // 目标网址
    "open_delay": 3                       // 延迟秒数
  }
}
```

### Cookie 设置
```json
{
  "cookies": {
    "enabled": true,           // 是否启用 Cookie 管理
    "file_path": "cookies.json", // Cookie 存储文件路径
    "auto_save": true          // 是否自动保存 Cookie
  }
}
```

## API 接口

### Cookie 管理 API

#### 获取 Cookie
```http
GET /api/cookies?domain=lmarena.ai
```

#### 更新 Cookie
```http
POST /api/cookies
Content-Type: application/json

{
  "domain": "lmarena.ai",
  "cookies": {
    "session_id": "abc123",
    "user_token": "xyz789"
  }
}
```

#### 清除 Cookie
```http
DELETE /api/cookies?domain=lmarena.ai
```

#### Cookie 统计
```http
GET /api/cookies/stats
```

### 浏览器管理 API

#### 手动打开浏览器
```http
POST /api/browser/open
Content-Type: application/json

{
  "url": "https://lmarena.ai/",
  "delay": 0
}
```

#### 获取浏览器配置
```http
GET /api/browser/config
```

## WebSocket 消息

### Cookie 更新消息
```json
{
  "type": "cookie_update",
  "domain": "lmarena.ai",
  "cookies": {
    "session_id": "abc123",
    "user_token": "xyz789"
  }
}
```

### Cookie 请求消息
```json
{
  "type": "cookie_request",
  "domain": "lmarena.ai"
}
```

## 使用方法

### 1. 启动服务器
```bash
python proxy_server.py
```

服务器启动后会：
- 自动在 3 秒后打开浏览器访问 https://lmarena.ai/
- 加载已保存的 Cookie
- 显示完整的访问地址信息

### 2. 在监控面板中管理
访问 `http://localhost:9080/monitor`，在"系统设置"部分可以：
- 配置浏览器自动打开设置
- 管理 Cookie 设置
- 查看 Cookie 统计信息
- 手动打开浏览器
- 清除 Cookie

### 3. 通过 API 管理
可以使用 HTTP API 或 WebSocket 消息来管理 Cookie 和浏览器功能。

## 文件结构

```
lmarena-proxy-main/
├── proxy_server.py          # 主服务器文件（已增强）
├── cookies.json             # Cookie 存储文件（自动生成）
├── logs/                    # 日志目录
│   ├── config.json         # 配置文件
│   ├── requests.jsonl      # 请求日志
│   └── errors.jsonl        # 错误日志
└── Cookie和浏览器功能说明.md # 本说明文档
```

## 注意事项

1. **Cookie 安全性**：Cookie 文件以明文 JSON 格式存储，请注意保护敏感信息
2. **浏览器兼容性**：自动打开功能会尝试使用系统默认浏览器，Windows 系统会优先尝试 Chrome
3. **网络访问**：确保能够访问 https://lmarena.ai/
4. **权限要求**：某些系统可能需要额外权限来自动打开浏览器

## 故障排除

### 浏览器无法自动打开
1. 检查系统是否安装了浏览器
2. 确认防火墙或安全软件没有阻止
3. 在监控面板中手动测试打开功能
4. 查看服务器日志获取详细错误信息

### Cookie 无法保存
1. 检查文件写入权限
2. 确认 Cookie 文件路径正确
3. 查看错误日志获取详细信息

## 更新日志

- **v1.1.0**：新增 Cookie 管理和自动打开浏览器功能
- 增加了完整的 API 接口
- 在监控面板中集成了设置界面
- 支持跨平台浏览器打开
- 添加了详细的日志记录
