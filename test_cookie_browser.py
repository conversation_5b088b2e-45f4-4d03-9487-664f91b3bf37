#!/usr/bin/env python3
"""
LMArena 代理服务器 Cookie 和浏览器功能测试脚本
"""

import requests
import json
import time
import sys

# 服务器配置
SERVER_URL = "http://localhost:9080"

def test_cookie_api():
    """测试 Cookie API 功能"""
    print("🍪 测试 Cookie API 功能...")
    
    # 测试数据
    test_domain = "lmarena.ai"
    test_cookies = {
        "session_id": "test_session_123",
        "user_token": "test_token_456",
        "preferences": "dark_mode=true"
    }
    
    try:
        # 1. 更新 Cookie
        print(f"1. 更新 {test_domain} 的 Cookie...")
        response = requests.post(f"{SERVER_URL}/api/cookies", json={
            "domain": test_domain,
            "cookies": test_cookies
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 成功: {result['message']}")
        else:
            print(f"   ❌ 失败: {response.status_code}")
            return False
        
        # 2. 获取 Cookie
        print(f"2. 获取 {test_domain} 的 Cookie...")
        response = requests.get(f"{SERVER_URL}/api/cookies", params={"domain": test_domain})
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 成功获取 {result['count']} 个 Cookie")
            print(f"   Cookie 内容: {json.dumps(result['cookies'], indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 失败: {response.status_code}")
            return False
        
        # 3. 获取 Cookie 统计
        print("3. 获取 Cookie 统计...")
        response = requests.get(f"{SERVER_URL}/api/cookies/stats")
        
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ 统计信息:")
            print(f"   - 域名数量: {stats['total_domains']}")
            print(f"   - Cookie 总数: {stats['total_cookies']}")
            print(f"   - 域名列表: {', '.join(stats['domains'])}")
        else:
            print(f"   ❌ 失败: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False

def test_browser_api():
    """测试浏览器 API 功能"""
    print("\n🌐 测试浏览器 API 功能...")
    
    try:
        # 1. 获取浏览器配置
        print("1. 获取浏览器配置...")
        response = requests.get(f"{SERVER_URL}/api/browser/config")
        
        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ 配置信息:")
            print(f"   - 自动打开: {config['auto_open']}")
            print(f"   - 目标网址: {config['target_url']}")
            print(f"   - 打开延迟: {config['open_delay']} 秒")
            print(f"   - 已打开状态: {config['opened']}")
        else:
            print(f"   ❌ 失败: {response.status_code}")
            return False
        
        # 2. 手动打开浏览器（可选，注释掉避免频繁打开）
        # print("2. 手动打开浏览器...")
        # response = requests.post(f"{SERVER_URL}/api/browser/open", json={
        #     "url": "https://lmarena.ai/",
        #     "delay": 0
        # })
        # 
        # if response.status_code == 200:
        #     result = response.json()
        #     print(f"   ✅ 成功: {result['message']}")
        # else:
        #     print(f"   ❌ 失败: {response.status_code}")
        #     return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False

def test_health_check():
    """测试服务器健康状态"""
    print("\n🏥 测试服务器健康状态...")
    
    try:
        response = requests.get(f"{SERVER_URL}/health")
        
        if response.status_code == 200:
            health = response.json()
            print(f"   ✅ 服务器状态: {health['status']}")
            print(f"   - 浏览器连接: {health['browser_connected']}")
            print(f"   - 活跃请求: {health['active_requests']}")
            print(f"   - 运行时间: {health['uptime']:.1f} 秒")
            print(f"   - 已加载模型: {health['models_loaded']}")
            return True
        else:
            print(f"   ❌ 失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False

def test_system_info():
    """测试系统信息"""
    print("\n📊 测试系统信息...")
    
    try:
        response = requests.get(f"{SERVER_URL}/api/system/info")
        
        if response.status_code == 200:
            info = response.json()
            print(f"   ✅ 系统信息:")
            print(f"   - 本地访问: {info['server_urls']['local']}")
            print(f"   - 网络访问: {info['server_urls']['network']}")
            print(f"   - 监控面板: {info['server_urls']['monitor']}")
            
            if info.get('ngrok_url'):
                print(f"   - Ngrok 地址: {info['ngrok_url']}")
            if info.get('localtunnel_url'):
                print(f"   - LocalTunnel 地址: {info['localtunnel_url']}")
            if info.get('serveo_url'):
                print(f"   - Serveo 地址: {info['serveo_url']}")
                
            return True
        else:
            print(f"   ❌ 失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        # 清除测试域名的 Cookie
        response = requests.delete(f"{SERVER_URL}/api/cookies", params={"domain": "lmarena.ai"})
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ {result['message']}")
        else:
            print(f"   ⚠️ 清理失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ⚠️ 清理异常: {e}")

def main():
    """主测试函数"""
    print("🚀 LMArena 代理服务器功能测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器未运行或无法访问: {SERVER_URL}")
            print("请先启动 proxy_server.py")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保 proxy_server.py 正在运行")
        sys.exit(1)
    
    print(f"✅ 服务器连接正常: {SERVER_URL}")
    
    # 运行测试
    tests = [
        test_health_check,
        test_system_info,
        test_cookie_api,
        test_browser_api,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # 短暂延迟
    
    # 清理测试数据
    cleanup_test_data()
    
    # 测试结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新功能工作正常。")
        print("\n💡 提示:")
        print(f"- 访问监控面板: {SERVER_URL}/monitor")
        print(f"- 查看 API 文档: {SERVER_URL}/monitor (点击 API 端点说明)")
        print("- 在监控面板的系统设置中可以配置 Cookie 和浏览器功能")
    else:
        print("⚠️ 部分测试失败，请检查服务器日志。")
        sys.exit(1)

if __name__ == "__main__":
    main()
