# LMArena Proxy - 可执行版本 (含智能重连)

## 新增功能

### 🔄 智能断线重连机制
- **自动重连**: 浏览器WebSocket断开时自动尝试重连
- **指数退避**: 重连间隔逐渐增加，避免频繁重试
- **连接稳定检测**: 连接稳定30秒后重置重连计数
- **最大重试次数**: 最多尝试10次重连
- **状态监控**: 在监控面板中显示重连状态

### 🔗 NewAPI 后缀适配
- **兼容端点**: 支持 /newapi/v1/* 格式的API调用
- **完全兼容**: NewAPI端点与标准端点功能完全一致
- **自动路由**: 无需额外配置，自动处理NewAPI格式请求

### 📡 Models端点POST支持
- **双方法支持**: /v1/models 现在同时支持 GET 和 POST 方法
- **兼容性增强**: 满足不同工具的API调用需求
- **响应一致**: GET 和 POST 方法返回完全相同的结果

### 🎯 智能路径识别
- **自动补全**: 简化路径自动重定向到完整API路径
- **多种变体**: 支持 /models、/chat、/refresh 等简化路径
- **零配置**: 无需额外设置，自动识别和处理

## 使用说明

### 启动服务器
1. 双击 `启动服务器.bat` 或直接运行 `LMArena-Proxy-Server.exe`
2. 等待服务器启动完成
3. 在浏览器中访问 http://localhost:9080/monitor

## 重要提示

1. **安装浏览器脚本**: 请确保已在Tampermonkey中安装了 `lmarena_injector.user.js` 脚本
2. **防火墙设置**: 首次运行时Windows可能会询问防火墙权限，请选择允许
3. **端口占用**: 默认使用9080端口，如果被占用请修改配置文件
4. **重连机制**: 浏览器脚本会自动处理连接断开和重连
5. **NewAPI兼容**: 支持 /newapi/v1/* 格式的API调用，与标准端点完全兼容
6. **POST方法支持**: /v1/models 端点现在同时支持 GET 和 POST 方法
7. **智能路径识别**: 自动识别简化路径并重定向到完整API路径

## 重连机制详情

### 服务器端
- 检测到浏览器断开连接时自动启动重连等待流程
- 保持待处理请求状态，等待浏览器重连
- 在监控面板显示重连状态和尝试次数

### 浏览器端
- 连接断开时自动尝试重连
- 使用指数退避算法，避免过于频繁的重连
- 重连成功后自动恢复待处理请求
- 连接稳定后重置重连计数

### 重连配置
- 最大重连次数: 10次
- 初始重连延迟: 1秒
- 最大重连延迟: 30秒
- 退避乘数: 1.5倍
- 连接稳定时间: 30秒

## 文件说明

- `LMArena-Proxy-Server.exe`: 服务器程序
- `lmarena_injector.user.js`: 浏览器脚本（含重连机制）
- `logs/`: 日志和配置文件目录
- `ngrok-v3-stable-windows-amd64/`: ngrok工具（如果包含）

## 原作者信息

- **作者**: zhongruichen
- **项目地址**: https://github.com/zhongruichen/lmarena-proxy
- **许可证**: MIT License
- **项目描述**: 一个功能强大的 LMArena 反向代理服务器，提供 OpenAI 兼容的 API 接口

## 源码获取

如需获取最新源码、报告问题或参与开发，请访问：
- GitHub仓库: https://github.com/zhongruichen/lmarena-proxy
- 问题反馈: https://github.com/zhongruichen/lmarena-proxy/issues
- 贡献指南: https://github.com/zhongruichen/lmarena-proxy#贡献

## 故障排除

如果遇到问题，请：
1. 检查防火墙设置
2. 确认端口9080未被占用
3. 查看logs目录下的日志文件
4. 检查浏览器控制台的重连日志
5. 访问原项目地址获取最新信息

## 访问地址

服务器启动后，可通过以下地址访问：
- 本地访问: http://localhost:9080
- 监控面板: http://localhost:9080/monitor
- API文档: http://localhost:9080/docs
- 健康检查: http://localhost:9080/api/health/detailed
- 重连状态: 在健康检查API中查看reconnection字段

## API 端点

### 标准端点
- POST http://localhost:9080/v1/chat/completions
- GET/POST http://localhost:9080/v1/models
- POST http://localhost:9080/v1/refresh-models

### NewAPI 兼容端点
- POST http://localhost:9080/newapi/v1/chat/completions
- GET/POST http://localhost:9080/newapi/v1/models
- POST http://localhost:9080/newapi/v1/refresh-models

### 智能路径识别 (自动补全)
- http://localhost:9080/models → /v1/models
- http://localhost:9080/chat → /v1/chat/completions
- http://localhost:9080/refresh → /v1/refresh-models

## 监控重连状态

在监控面板中可以看到：
- 浏览器连接状态
- 重连尝试次数
- 重连进度
- 连接稳定时间
