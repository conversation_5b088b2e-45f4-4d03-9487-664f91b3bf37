# LMArena 代理服务器 - 内嵌页面功能测试指南

## 🎯 新功能概述

本次更新在监控面板中增加了内嵌 LMArena 页面功能，用户可以直接在监控面板中使用 LMArena，无需切换窗口。

## 🚀 功能特性

### 1. 内嵌页面显示
- ✅ 在监控面板中直接显示 LMArena 页面
- ✅ 可自定义页面 URL
- ✅ 支持四种尺寸：小(400px)、中(600px)、大(800px)、超大(1000px)
- ✅ 设置自动保存到浏览器本地存储

### 2. 交互控制
- ✅ 刷新按钮：重新加载页面
- ✅ 全屏按钮：切换全屏显示模式
- ✅ 新标签页按钮：在新标签页中打开
- ✅ 关闭按钮：隐藏内嵌页面

### 3. 状态显示
- ✅ 加载状态：显示加载动画
- ✅ 错误处理：显示错误信息和重试按钮
- ✅ 超时检测：30秒加载超时保护

## 📋 测试步骤

### 步骤 1: 启动服务器
```bash
python proxy_server.py
```

### 步骤 2: 访问监控面板
打开浏览器访问：`http://localhost:9080/monitor`

### 步骤 3: 启用内嵌页面
1. 滚动到页面底部的"LMArena 内嵌页面"设置区域
2. 勾选"显示内嵌页面"复选框
3. 页面会自动显示内嵌的 LMArena 页面

### 步骤 4: 测试功能
1. **调整大小**：
   - 在"页面高度"下拉菜单中选择不同尺寸
   - 观察内嵌页面高度变化

2. **更换 URL**：
   - 在"页面地址"输入框中输入其他网址
   - 按回车键或点击其他地方，页面会自动更新

3. **刷新功能**：
   - 点击页面头部的"🔄"按钮
   - 观察页面重新加载

4. **全屏模式**：
   - 点击页面头部的"⛶"按钮
   - 页面会进入全屏模式
   - 再次点击或按 ESC 键退出全屏

5. **新标签页打开**：
   - 点击页面头部的"🔗"按钮
   - 会在新标签页中打开当前 URL

6. **关闭页面**：
   - 点击页面头部的"✕"按钮
   - 内嵌页面会隐藏，复选框会取消勾选

## 🔧 配置说明

### 默认设置
- **页面地址**：`https://lmarena.ai/`
- **页面高度**：600px (中等尺寸)
- **显示状态**：默认关闭

### 设置保存
所有设置都会自动保存到浏览器的本地存储中：
- `iframe-enabled`: 是否启用内嵌页面
- `iframe-url`: 内嵌页面的 URL
- `iframe-height`: 内嵌页面的高度

### 安全设置
内嵌页面使用了以下安全沙箱设置：
```html
sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-popups-to-escape-sandbox"
```

## 🎨 界面预览

### 设置区域
```
🤖 LMArena 内嵌页面
☑ 显示内嵌页面: [✓]
📝 页面地址: [https://lmarena.ai/        ]
📏 页面高度: [中 (600px) ▼]
[保存浏览器设置] [立即打开浏览器]
```

### 内嵌页面
```
┌─────────────────────────────────────────────────────┐
│ 🌐 LMArena 页面              🔄 ⛶ 🔗 ✕ │
├─────────────────────────────────────────────────────┤
│                                                     │
│           LMArena 页面内容                          │
│                                                     │
│                                                     │
└─────────────────────────────────────────────────────┘
```

## 🐛 故障排除

### 常见问题

1. **页面无法加载**
   - 检查网络连接
   - 确认 URL 地址正确
   - 点击刷新按钮重试

2. **页面显示空白**
   - 某些网站可能不允许在 iframe 中显示
   - 尝试点击"新标签页打开"按钮

3. **全屏模式无法使用**
   - 确保浏览器支持全屏 API
   - 某些浏览器需要用户手势触发

4. **设置不保存**
   - 检查浏览器是否允许本地存储
   - 清除浏览器缓存后重试

### 调试方法

1. **查看浏览器控制台**：
   - 按 F12 打开开发者工具
   - 查看 Console 标签页的错误信息

2. **检查网络请求**：
   - 在开发者工具的 Network 标签页查看请求状态

3. **验证本地存储**：
   - 在开发者工具的 Application > Local Storage 中查看设置

## 📊 性能考虑

- **内存使用**：内嵌页面会增加一定的内存使用
- **网络流量**：页面加载会产生额外的网络请求
- **CPU 使用**：页面渲染会占用一定的 CPU 资源

建议在性能较低的设备上谨慎使用大尺寸的内嵌页面。

## 🔮 未来改进

1. **多标签页支持**：支持在内嵌区域显示多个标签页
2. **页面缩放**：支持页面缩放功能
3. **历史记录**：保存访问过的页面历史
4. **书签功能**：快速访问常用页面
5. **主题适配**：根据监控面板主题调整内嵌页面样式

## ✅ 测试清单

- [ ] 启用/禁用内嵌页面功能
- [ ] 调整页面高度（四种尺寸）
- [ ] 更换页面 URL
- [ ] 刷新页面功能
- [ ] 全屏模式切换
- [ ] 新标签页打开
- [ ] 关闭页面功能
- [ ] 设置自动保存
- [ ] 加载状态显示
- [ ] 错误处理和重试
- [ ] 页面加载超时检测

## 📝 反馈

如果在使用过程中遇到问题或有改进建议，请：
1. 查看浏览器控制台的错误信息
2. 检查服务器日志
3. 记录具体的操作步骤和错误现象

---

**注意**：内嵌页面功能需要目标网站支持在 iframe 中显示。某些网站（如 Google、Facebook 等）出于安全考虑可能不允许在 iframe 中加载。
