#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LMArena Proxy 简单打包脚本
只打包服务器程序，不包含GUI

原作者: zhongruichen
项目地址: https://github.com/zhongruichen/lmarena-proxy
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("✗ PyInstaller 未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller 安装失败: {e}")
        return False

def build_server():
    """构建服务器exe"""
    print("开始构建服务器exe文件...")
    
    try:
        # 直接使用PyInstaller命令构建
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",  # 打包成单个exe文件
            "--console",  # 显示控制台窗口
            "--name", "LMArena-Proxy-Server",
            "--add-data", "lmarena_injector.user.js;.",
            "--add-data", "requirements.txt;.",
            "--add-data", "README.md;.",
            "--add-data", "LICENSE;.",
            "--add-data", "docs;docs",
            "--noconfirm",
            "proxy_server.py"
        ]
        
        subprocess.check_call(cmd)
        print("✓ 服务器构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 服务器构建失败: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到dist目录"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("✗ dist目录不存在")
        return False
    
    # 复制ngrok文件夹（如果存在）
    ngrok_dir = Path("ngrok-v3-stable-windows-amd64")
    if ngrok_dir.exists():
        shutil.copytree(ngrok_dir, dist_dir / "ngrok-v3-stable-windows-amd64", dirs_exist_ok=True)
        print("✓ 已复制 ngrok 文件")
    
    # 创建logs目录
    logs_dir = dist_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # 复制配置文件（如果存在）
    config_file = Path("logs/config.json")
    if config_file.exists():
        shutil.copy2(config_file, logs_dir / "config.json")
        print("✓ 已复制配置文件")
    
    # 复制浏览器脚本到dist根目录
    script_file = Path("lmarena_injector.user.js")
    if script_file.exists():
        shutil.copy2(script_file, dist_dir / "lmarena_injector.user.js")
        print("✓ 已复制浏览器脚本")
    
    # 创建启动脚本
    start_script = dist_dir / "启动服务器.bat"
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('cd /d "%~dp0"\n')
        f.write('echo 正在启动 LMArena Proxy 服务器...\n')
        f.write('echo 原作者: zhongruichen\n')
        f.write('echo 源码地址: https://github.com/zhongruichen/lmarena-proxy\n')
        f.write('echo 新增功能: 智能断线重连 + NewAPI适配\n')
        f.write('echo.\n')
        f.write('LMArena-Proxy-Server.exe\n')
        f.write('pause\n')
    
    print("✓ 已创建启动脚本")
    
    return True

def create_readme():
    """创建使用说明"""
    dist_dir = Path("dist")
    readme_content = """# LMArena Proxy - 可执行版本 (含智能重连)

## 新增功能

### 🔄 智能断线重连机制
- **自动重连**: 浏览器WebSocket断开时自动尝试重连
- **指数退避**: 重连间隔逐渐增加，避免频繁重试
- **连接稳定检测**: 连接稳定30秒后重置重连计数
- **最大重试次数**: 最多尝试10次重连
- **状态监控**: 在监控面板中显示重连状态

### 🔗 NewAPI 后缀适配
- **兼容端点**: 支持 /newapi/v1/* 格式的API调用
- **完全兼容**: NewAPI端点与标准端点功能完全一致
- **自动路由**: 无需额外配置，自动处理NewAPI格式请求





## 使用说明

### 启动服务器
1. 双击 `启动服务器.bat` 或直接运行 `LMArena-Proxy-Server.exe`
2. 等待服务器启动完成
3. 在浏览器中访问 http://localhost:9080/monitor

## 重要提示

1. **安装浏览器脚本**: 请确保已在Tampermonkey中安装了 `lmarena_injector.user.js` 脚本
2. **防火墙设置**: 首次运行时Windows可能会询问防火墙权限，请选择允许
3. **端口占用**: 默认使用9080端口，如果被占用请修改配置文件
4. **重连机制**: 浏览器脚本会自动处理连接断开和重连
5. **NewAPI兼容**: 支持 /newapi/v1/* 格式的API调用，与标准端点完全兼容



## 重连机制详情

### 服务器端
- 检测到浏览器断开连接时自动启动重连等待流程
- 保持待处理请求状态，等待浏览器重连
- 在监控面板显示重连状态和尝试次数

### 浏览器端
- 连接断开时自动尝试重连
- 使用指数退避算法，避免过于频繁的重连
- 重连成功后自动恢复待处理请求
- 连接稳定后重置重连计数

### 重连配置
- 最大重连次数: 10次
- 初始重连延迟: 1秒
- 最大重连延迟: 30秒
- 退避乘数: 1.5倍
- 连接稳定时间: 30秒

## 文件说明

- `LMArena-Proxy-Server.exe`: 服务器程序
- `lmarena_injector.user.js`: 浏览器脚本（含重连机制）
- `logs/`: 日志和配置文件目录
- `ngrok-v3-stable-windows-amd64/`: ngrok工具（如果包含）

## 原作者信息

- **作者**: zhongruichen
- **项目地址**: https://github.com/zhongruichen/lmarena-proxy
- **许可证**: MIT License
- **项目描述**: 一个功能强大的 LMArena 反向代理服务器，提供 OpenAI 兼容的 API 接口

## 源码获取

如需获取最新源码、报告问题或参与开发，请访问：
- GitHub仓库: https://github.com/zhongruichen/lmarena-proxy
- 问题反馈: https://github.com/zhongruichen/lmarena-proxy/issues
- 贡献指南: https://github.com/zhongruichen/lmarena-proxy#贡献

## 故障排除

如果遇到问题，请：
1. 检查防火墙设置
2. 确认端口9080未被占用
3. 查看logs目录下的日志文件
4. 检查浏览器控制台的重连日志
5. 访问原项目地址获取最新信息

## 访问地址

服务器启动后，可通过以下地址访问：
- 本地访问: http://localhost:9080
- 监控面板: http://localhost:9080/monitor
- API文档: http://localhost:9080/docs
- 健康检查: http://localhost:9080/api/health/detailed
- 重连状态: 在健康检查API中查看reconnection字段

## API 端点

### 标准端点
- POST http://localhost:9080/v1/chat/completions
- GET http://localhost:9080/v1/models
- POST http://localhost:9080/v1/refresh-models

### NewAPI 兼容端点
- POST http://localhost:9080/newapi/v1/chat/completions
- GET http://localhost:9080/newapi/v1/models
- POST http://localhost:9080/newapi/v1/refresh-models



## 监控重连状态

在监控面板中可以看到：
- 浏览器连接状态
- 重连尝试次数
- 重连进度
- 连接稳定时间
"""
    
    readme_file = dist_dir / "使用说明.txt"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 已创建使用说明")

def main():
    """主函数"""
    print("LMArena Proxy 简单打包工具 (含智能重连)")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，请手动安装")
            return False
    
    # 构建服务器exe
    if not build_server():
        return False
    
    # 复制额外文件
    if not copy_additional_files():
        return False
    
    # 创建说明文件
    create_readme()
    
    print("\n" + "=" * 50)
    print("✓ 打包完成！")
    print("输出目录: dist/")
    print("主程序: dist/LMArena-Proxy-Server.exe")
    print("启动脚本: dist/启动服务器.bat")
    print("新增功能: 智能断线重连 + NewAPI适配")
    print("请查看 '使用说明.txt' 了解如何使用")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n打包失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生错误: {e}")
        sys.exit(1)
